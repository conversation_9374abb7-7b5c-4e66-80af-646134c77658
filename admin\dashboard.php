<?php
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['userId']) || $_SESSION['userType'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

require_once '../includes/dbh.inc.php';

// Include auto backup check script
require_once '../includes/check_auto_backup.php';

// Check if essential tables exist
$requiredTables = ['students', 'teachers', 'staff', 'classes', 'departments', 'notices'];
$missingTables = [];

foreach ($requiredTables as $table) {
    $tableCheck = $conn->query("SHOW TABLES LIKE '$table'");
    if ($tableCheck->num_rows == 0) {
        $missingTables[] = $table;
    }
}

if (!empty($missingTables)) {
    echo "<div class='alert alert-danger'>
            <strong>Warning:</strong> Some required tables are missing: " . implode(', ', $missingTables) . ".
            <div class='mt-2'>
                <a href='../fix_database_tables.php' class='btn btn-primary me-2'>
                    <i class='fas fa-wrench'></i> Fix Database Tables
                </a>
                <a href='../create_staff_table.php' class='btn btn-success me-2'>
                    <i class='fas fa-user-tie'></i> Create Staff Table
                </a>
                <a href='../create_essential_tables.php' class='btn btn-warning'>
                    <i class='fas fa-database'></i> Create All Tables
                </a>
            </div>
          </div>";
}

// Get stats for dashboard
$statsQuery = [
    'students' => "SELECT COUNT(*) as count FROM students",
    'teachers' => "SELECT COUNT(*) as count FROM teachers",
    'staff' => "SELECT COUNT(*) as count FROM staff",
    'classes' => "SELECT COUNT(*) as count FROM classes",
    // Additional statistics can be added here as needed
];

// Check if bkash_payments table exists and get bKash payment stats
$bkashStatsQuery = "SHOW TABLES LIKE 'bkash_payments'";
$bkashTableExists = $conn->query($bkashStatsQuery)->num_rows > 0;

$bkashStats = [
    'total_payments' => 0,
    'completed_amount' => 0
];

if ($bkashTableExists) {
    $bkashPaymentsQuery = "SELECT
                            COUNT(*) as total_payments,
                            SUM(CASE WHEN status = 'Completed' THEN amount ELSE 0 END) as completed_amount
                          FROM bkash_payments";
    $bkashResult = $conn->query($bkashPaymentsQuery);

    if ($bkashResult && $bkashResult->num_rows > 0) {
        $bkashStats = $bkashResult->fetch_assoc();
    }
}

$stats = [];
foreach ($statsQuery as $key => $query) {
    // Check if table exists before querying
    $tableCheck = $conn->query("SHOW TABLES LIKE '$key'");
    if ($tableCheck->num_rows > 0) {
        $result = $conn->query($query);
        if ($result) {
            $row = $result->fetch_assoc();
            $stats[$key] = $row['count'];
        } else {
            $stats[$key] = 0;
        }
    } else {
        $stats[$key] = 0;
    }
}

// Get recent students if tables exist
$recentStudents = null;
if (!in_array('students', $missingTables)) {
    // First check if department_id column exists in students table
    $deptIdExists = $conn->query("SHOW COLUMNS FROM students LIKE 'department_id'");

    // Check if admission_date column exists
    $admissionDateExists = $conn->query("SHOW COLUMNS FROM students LIKE 'admission_date'");
    $admissionDateField = ($admissionDateExists && $admissionDateExists->num_rows > 0) ? 's.admission_date' : 's.created_at';
    $admissionDateFieldSimple = ($admissionDateExists && $admissionDateExists->num_rows > 0) ? 'admission_date' : 'created_at';

    if ($deptIdExists && $deptIdExists->num_rows > 0 && !in_array('departments', $missingTables)) {
        // If department_id exists and departments table exists, use the join
        $recentStudentsQuery = "SELECT s.student_id, s.first_name, s.last_name, d.department_name, {$admissionDateField} as admission_date
                              FROM students s
                              LEFT JOIN departments d ON s.department_id = d.id
                              ORDER BY s.id DESC
                              LIMIT 5";
    } else {
        // Otherwise, just get student data without department info
        $recentStudentsQuery = "SELECT student_id, first_name, last_name, NULL as department_name, {$admissionDateFieldSimple} as admission_date
                              FROM students
                              ORDER BY id DESC
                              LIMIT 5";
    }

    $recentStudents = $conn->query($recentStudentsQuery);
}

// Get recent teachers if tables exist
$recentTeachers = null;
if (!in_array('teachers', $missingTables)) {
    // First check if department_id column exists in teachers table
    $deptIdExists = $conn->query("SHOW COLUMNS FROM teachers LIKE 'department_id'");

    // Check if joining_date column exists
    $joiningDateExists = $conn->query("SHOW COLUMNS FROM teachers LIKE 'joining_date'");
    $joiningDateField = ($joiningDateExists && $joiningDateExists->num_rows > 0) ? 't.joining_date' : 't.created_at';
    $joiningDateFieldSimple = ($joiningDateExists && $joiningDateExists->num_rows > 0) ? 'joining_date' : 'created_at';

    if ($deptIdExists && $deptIdExists->num_rows > 0 && !in_array('departments', $missingTables)) {
        // If department_id exists and departments table exists, use the join
        $recentTeachersQuery = "SELECT t.teacher_id, t.first_name, t.last_name, d.department_name, {$joiningDateField} as joining_date
                              FROM teachers t
                              LEFT JOIN departments d ON t.department_id = d.id
                              ORDER BY t.id DESC
                              LIMIT 5";
    } else {
        // Otherwise, just get teacher data without department info
        $recentTeachersQuery = "SELECT teacher_id, first_name, last_name, NULL as department_name, {$joiningDateFieldSimple} as joining_date
                              FROM teachers
                              ORDER BY id DESC
                              LIMIT 5";
    }

    $recentTeachers = $conn->query($recentTeachersQuery);
}

// Get upcoming exams if exams table exists
$upcomingExams = null;
$examsTableCheck = $conn->query("SHOW TABLES LIKE 'exams'");
if ($examsTableCheck->num_rows > 0) {
    $upcomingExamsQuery = "SELECT e.exam_name, e.exam_date, e.total_marks, s.subject_name
                         FROM exams e
                         LEFT JOIN subjects s ON e.subject_id = s.id
                         WHERE e.exam_date >= CURDATE()
                         ORDER BY e.exam_date ASC
                         LIMIT 5";
    $upcomingExams = $conn->query($upcomingExamsQuery);
}
?>

<!DOCTYPE html>
<html lang="bn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex, nofollow">
    <title>ড্যাশবোর্ড - কলেজ ম্যানেজমেন্ট সিস্টেম</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Hind Siliguri Font CSS -->
    <link rel="stylesheet" href="css/hind-siliguri.css">

    <style>
        /* Font Settings */
        body, html, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, select, textarea, label, li, table, th, td {
            font-family: 'Hind Siliguri', sans-serif !important;
        }

        body {
            background-color: #f8f9fa;
        }

        /* Sidebar Styles */
        .sidebar {
            background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%);
            color: white;
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            width: 16.66%;
            overflow-y: auto;
            padding-top: 20px;
            padding-bottom: 60px;
            z-index: 100;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.9);
            padding: 10px 20px;
            margin-bottom: 5px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.15);
            color: white;
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            background: linear-gradient(to right, #3498db, #2980b9);
            color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .sidebar .nav-item {
            margin-bottom: 2px;
        }

        .main-content {
            margin-left: 16.66%;
            padding: 25px;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background-color: #fff;
            padding: 15px 20px;
        }

        .card-body {
            padding: 20px;
        }

        /* Stat Cards */
        .stat-card {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-card .card-body {
            padding: 25px;
        }

        .stat-card i {
            opacity: 0.8;
        }

        /* Card Styles */
        .card-pattern {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 0;
            opacity: 0.5;
            background-size: 100px 100px;
            background-repeat: repeat;
        }

        .card-shine {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 50%);
        }

        .stat-card {
            transition: all 0.3s ease;
            border: none;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .stat-card .card-body {
            border-radius: 12px;
        }

        .icon-circle {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover .icon-circle {
            transform: scale(1.1);
        }

        .bg-opacity-25 {
            --bs-bg-opacity: 0.25;
        }

        .position-relative {
            position: relative !important;
        }

        .position-absolute {
            position: absolute !important;
        }

        .z-index-1 {
            z-index: 1 !important;
        }

        .opacity-10 {
            opacity: 0.1 !important;
        }

        /* Quick Access Styles */
        .quick-access-bar {
            border-radius: 12px;
            overflow: hidden;
            transition: all 0.3s ease;
            background-color: #fff;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .quick-access-bar .btn {
            border-radius: 8px;
            padding: 10px 15px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 5px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            min-width: 160px;
        }

        .quick-access-bar .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .quick-access-bar .nav-tabs {
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 0 10px;
        }

        .quick-access-bar .nav-tabs .nav-link {
            border: none;
            padding: 12px 20px;
            font-weight: 500;
            color: #6c757d;
            border-radius: 0;
            transition: all 0.3s ease;
        }

        .quick-access-bar .nav-tabs .nav-link.active {
            color: #4e73df;
            border-bottom: 2px solid #4e73df;
            background-color: transparent;
        }

        .quick-access-bar .nav-tabs .nav-link:hover:not(.active) {
            color: #4e73df;
            background-color: rgba(78, 115, 223, 0.05);
        }

        .quick-access-bar .tab-content {
            padding: 15px;
        }

        /* Table Styles */
        .table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .table td, .table th {
            padding: 12px 15px;
            vertical-align: middle;
        }

        /* Responsive Adjustments */
        @media (max-width: 991.98px) {
            .sidebar {
                width: 25%;
            }

            .main-content {
                margin-left: 25%;
            }
        }

        @media (max-width: 767.98px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }

            .main-content {
                margin-left: 0;
            }

            .stat-card {
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <div class="text-center mb-4">
                    <h3>অ্যাডমিন প্যানেল</h3>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i> ড্যাশবোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="students.php">
                            <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="teachers.php">
                            <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="classes.php">
                            <i class="fas fa-school me-2"></i> শ্রেণী
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subjects.php">
                            <i class="fas fa-book me-2"></i> বিষয়
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="attendance.php">
                            <i class="fas fa-calendar-check me-2"></i> উপস্থিতি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="http://localhost/zfaw/admin/exam_dashboard.php">
                            <i class="fas fa-file-alt me-2"></i> পরীক্ষা ব্যবস্থাপনা
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="subject_exam_pattern.php">
                            <i class="fas fa-sliders-h me-2"></i> বিষয় পরীক্ষা প্যাটার্ন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subject_marks_distribution.php">
                            <i class="fas fa-chart-pie me-2"></i> বিষয় মার্কস ডিস্ট্রিবিউশন
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subject_minimum_pass.php">
                            <i class="fas fa-check-circle me-2"></i> ন্যূনতম পাস মার্কস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="results.php">
                            <i class="fas fa-chart-bar me-2"></i> ফলাফল
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="result_management.php">
                            <i class="fas fa-edit me-2"></i> মার্কস এন্ট্রি
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="generate_marksheet.php">
                            <i class="fas fa-file-alt me-2"></i> মার্কশীট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="tabulation_sheet.php">
                            <i class="fas fa-table me-2"></i> ট্যাবুলেশন শীট
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="student_shortcodes.php">
                            <i class="fas fa-hashtag me-2"></i> শিক্ষার্থী সর্টকোড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="generate_id_cards.php">
                            <i class="fas fa-id-card me-2"></i> শিক্ষার্থী আইডি কার্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="notices.php">
                            <i class="fas fa-bullhorn me-2"></i> নোটিশ
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="settings.php">
                            <i class="fas fa-cog me-2"></i> সেটিংস
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../database/check_tables.php">
                            <i class="fas fa-database me-2"></i> ডাটাবেস চেক
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../fix_database_tables.php">
                            <i class="fas fa-wrench me-2"></i> ডাটাবেস ফিক্স
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../upload_content.php">
                            <i class="fas fa-upload me-2"></i> কনটেন্ট আপলোড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="subject_csv_upload.php">
                            <i class="fas fa-file-csv me-2"></i> বিষয় CSV আপলোড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="csv_upload.php">
                            <i class="fas fa-upload me-2"></i> CSV আপলোড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../update_features.php">
                            <i class="fas fa-cogs me-2"></i> বৈশিষ্ট্য আপডেট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../update_about.php">
                            <i class="fas fa-info-circle me-2"></i> আমাদের সম্পর্কে আপডেট
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_gb_members.php">
                            <i class="fas fa-users me-2"></i> পরিচালনা বোর্ড
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../includes/logout.inc.php">
                            <i class="fas fa-sign-out-alt me-2"></i> লগআউট
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Quick Access Menu Bar -->
                <div class="quick-access-bar bg-white shadow mb-4">
                    <div class="d-flex justify-content-between align-items-center px-4 py-3 border-bottom">
                        <h5 class="mb-0"><i class="fas fa-bolt text-warning me-2"></i>দ্রুত অ্যাক্সেস</h5>
                        <span class="badge bg-primary">সহজ নেভিগেশন</span>
                    </div>

                    <!-- Main Quick Access Categories -->
                    <div class="p-3">
                        <ul class="nav nav-tabs" id="quickAccessTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="students-tab" data-bs-toggle="tab" data-bs-target="#students" type="button" role="tab" aria-controls="students" aria-selected="true">
                                    <i class="fas fa-user-graduate me-2"></i> শিক্ষার্থী
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="teachers-tab" data-bs-toggle="tab" data-bs-target="#teachers" type="button" role="tab" aria-controls="teachers" aria-selected="false">
                                    <i class="fas fa-chalkboard-teacher me-2"></i> শিক্ষক
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="exams-tab" data-bs-toggle="tab" data-bs-target="#exams" type="button" role="tab" aria-controls="exams" aria-selected="false">
                                    <i class="fas fa-file-alt me-2"></i> পরীক্ষা
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="fees-tab" data-bs-toggle="tab" data-bs-target="#fees" type="button" role="tab" aria-controls="fees" aria-selected="false">
                                    <i class="fas fa-money-bill-wave me-2"></i> ফি
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="admin-tab" data-bs-toggle="tab" data-bs-target="#admin" type="button" role="tab" aria-controls="admin" aria-selected="false">
                                    <i class="fas fa-shield-alt me-2"></i> এডমিন
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content pt-3" id="quickAccessTabContent">
                            <!-- Students Tab -->
                            <div class="tab-pane fade show active" id="students" role="tabpanel" aria-labelledby="students-tab">
                                <div class="d-flex flex-wrap">
                                    <a class="btn btn-primary m-1 d-flex align-items-center" href="add_student.php">
                                        <i class="fas fa-user-plus me-2"></i> নতুন শিক্ষার্থী
                                    </a>
                                    <a class="btn btn-outline-primary m-1 d-flex align-items-center" href="students.php">
                                        <i class="fas fa-list me-2"></i> শিক্ষার্থী তালিকা
                                    </a>
                                    <a class="btn btn-outline-primary m-1 d-flex align-items-center" href="generate_id_cards.php">
                                        <i class="fas fa-id-card me-2"></i> আইডি কার্ড
                                    </a>
                                    <a class="btn btn-outline-primary m-1 d-flex align-items-center" href="student_shortcodes.php">
                                        <i class="fas fa-hashtag me-2"></i> সর্টকোড
                                    </a>

                                    <a class="btn btn-outline-primary m-1 d-flex align-items-center" href="advanced_certificate.php">
                                        <i class="fas fa-certificate me-2"></i> সনদ
                                    </a>
                                    <a class="btn btn-outline-success m-1 d-flex align-items-center" href="subject_csv_upload.php">
                                        <i class="fas fa-file-csv me-2"></i> বিষয় CSV আপলোড
                                    </a>
                                    <a class="btn btn-outline-success m-1 d-flex align-items-center" href="csv_upload.php">
                                        <i class="fas fa-upload me-2"></i> CSV আপলোড
                                    </a>
                                    <a class="btn btn-outline-warning m-1 d-flex align-items-center" href="fix_subject_categories.php">
                                        <i class="fas fa-tools me-2"></i> বিষয় ক্যাটাগরি ঠিক করুন
                                    </a>
                                    <a class="btn btn-outline-info m-1 d-flex align-items-center" href="update_subjects_table.php">
                                        <i class="fas fa-database me-2"></i> বিষয় টেবিল আপডেট
                                    </a>
                                    <a class="btn btn-outline-dark m-1 d-flex align-items-center" href="test_subjects_functionality.php">
                                        <i class="fas fa-vial me-2"></i> বিষয় ফাংশন টেস্ট
                                    </a>
                                </div>
                            </div>

                            <!-- Teachers Tab -->
                            <div class="tab-pane fade" id="teachers" role="tabpanel" aria-labelledby="teachers-tab">
                                <div class="d-flex flex-wrap">
                                    <a class="btn btn-success m-1 d-flex align-items-center" href="add_teacher.php">
                                        <i class="fas fa-chalkboard-teacher me-2"></i> নতুন শিক্ষক
                                    </a>
                                    <a class="btn btn-outline-success m-1 d-flex align-items-center" href="teachers.php">
                                        <i class="fas fa-list me-2"></i> শিক্ষক তালিকা
                                    </a>
                                    <a class="btn btn-outline-success m-1 d-flex align-items-center" href="add_staff.php">
                                        <i class="fas fa-user-tie me-2"></i> নতুন কর্মচারী
                                    </a>
                                    <a class="btn btn-outline-success m-1 d-flex align-items-center" href="staff.php">
                                        <i class="fas fa-list me-2"></i> কর্মচারী তালিকা
                                    </a>
                                    <a class="btn btn-outline-success m-1 d-flex align-items-center" href="manage_gb_members.php">
                                        <i class="fas fa-users me-2"></i> পরিচালনা বোর্ড
                                    </a>
                                </div>
                            </div>

                            <!-- Exams Tab -->
                            <div class="tab-pane fade" id="exams" role="tabpanel" aria-labelledby="exams-tab">
                                <div class="d-flex flex-wrap">
                                    <a class="btn btn-danger m-1 d-flex align-items-center" href="http://localhost/zfaw/admin/exam_dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i> পরীক্ষা ব্যবস্থাপনা ড্যাশবোর্ড
                                    </a>
                                    <a class="btn btn-warning m-1 d-flex align-items-center" href="create_exam.php">
                                        <i class="fas fa-plus-circle me-2"></i> নতুন পরীক্ষা
                                    </a>
                                    <a class="btn btn-outline-warning m-1 d-flex align-items-center" href="manage_exams.php">
                                        <i class="fas fa-list me-2"></i> পরীক্ষা তালিকা
                                    </a>
                                    <a class="btn btn-outline-warning m-1 d-flex align-items-center" href="exam_types.php">
                                        <i class="fas fa-tags me-2"></i> পরীক্ষার ধরন
                                    </a>
                                    <a class="btn btn-outline-warning m-1 d-flex align-items-center" href="subject_exam_pattern.php">
                                        <i class="fas fa-sliders-h me-2"></i> পরীক্ষা প্যাটার্ন
                                    </a>
                                    <a class="btn btn-outline-warning m-1 d-flex align-items-center" href="subject_marks_distribution.php">
                                        <i class="fas fa-chart-pie me-2"></i> মার্কস ডিস্ট্রিবিউশন
                                    </a>
                                    <a class="btn btn-outline-warning m-1 d-flex align-items-center" href="subject_minimum_pass.php">
                                        <i class="fas fa-check-circle me-2"></i> ন্যূনতম পাস মার্কস
                                    </a>
                                    <a class="btn btn-outline-warning m-1 d-flex align-items-center" href="results.php">
                                        <i class="fas fa-chart-bar me-2"></i> ফলাফল
                                    </a>
                                    <a class="btn btn-warning m-1 d-flex align-items-center" href="result_management.php">
                                        <i class="fas fa-edit me-2"></i> মার্কস এন্ট্রি
                                    </a>
                                    <a class="btn btn-outline-warning m-1 d-flex align-items-center" href="generate_marksheet.php">
                                        <i class="fas fa-file-alt me-2"></i> মার্কশীট
                                    </a>
                                    <a class="btn btn-outline-warning m-1 d-flex align-items-center" href="tabulation_sheet.php">
                                        <i class="fas fa-table me-2"></i> ট্যাবুলেশন শীট
                                    </a>

                                </div>
                            </div>

                            <!-- Fees Tab -->
                            <div class="tab-pane fade" id="fees" role="tabpanel" aria-labelledby="fees-tab">
                                <div class="d-flex flex-wrap">
                                    <a class="btn btn-info m-1 d-flex align-items-center" href="fee_management.php">
                                        <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                                    </a>
                                    <a class="btn btn-danger m-1 d-flex align-items-center" href="due_fees.php">
                                        <i class="fas fa-exclamation-circle me-2"></i> বকেয়া বেতন
                                    </a>
                                    <a class="btn btn-outline-info m-1 d-flex align-items-center" href="fee_types.php">
                                        <i class="fas fa-tags me-2"></i> ফি টাইপ
                                    </a>
                                    <a class="btn btn-outline-info m-1 d-flex align-items-center" href="fee_categories.php">
                                        <i class="fas fa-folder me-2"></i> ফি ক্যাটাগরি
                                    </a>
                                    <a class="btn btn-outline-info m-1 d-flex align-items-center" href="fee_report.php">
                                        <i class="fas fa-file-alt me-2"></i> ফি রিপোর্ট
                                    </a>
                                    <a class="btn btn-outline-info m-1 d-flex align-items-center" href="update_fees_table.php">
                                        <i class="fas fa-database me-2"></i> ডাটাবেস আপডেট
                                    </a>
                                </div>
                            </div>

                            <!-- Admin Tab -->
                            <div class="tab-pane fade" id="admin" role="tabpanel" aria-labelledby="admin-tab">
                                <div class="d-flex flex-wrap">
                                    <a class="btn btn-danger m-1 d-flex align-items-center" href="notices.php">
                                        <i class="fas fa-bullhorn me-2"></i> নোটিশ
                                    </a>

                                    <a class="btn btn-danger m-1 d-flex align-items-center" href="fee_management.php">
                                        <i class="fas fa-money-bill-wave me-2"></i> ফি ম্যানেজমেন্ট
                                    </a>

                                    <a class="btn btn-outline-danger m-1 d-flex align-items-center" href="settings.php">
                                        <i class="fas fa-cog me-2"></i> সেটিংস
                                    </a>
                                    <a class="btn btn-outline-danger m-1 d-flex align-items-center" href="../database/check_tables.php">
                                        <i class="fas fa-database me-2"></i> ডাটাবেস চেক
                                    </a>
                                    <a class="btn btn-outline-danger m-1 d-flex align-items-center" href="../fix_database_tables.php">
                                        <i class="fas fa-wrench me-2"></i> ডাটাবেস ফিক্স
                                    </a>
                                    <a class="btn btn-danger m-1 d-flex align-items-center" href="database_backup.php">
                                        <i class="fas fa-download me-2"></i> ডাটাবেস ব্যাকআপ
                                    </a>
                                    <a class="btn btn-outline-danger m-1 d-flex align-items-center" href="../update_all.php">
                                        <i class="fas fa-sync me-2"></i> আপডেট
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col">
                        <h2>ড্যাশবোর্ড</h2>
                        <p class="text-muted">সিস্টেমের সামগ্রিক পরিসংখ্যান এবং দ্রুত অ্যাক্সেস</p>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body position-relative overflow-hidden" style="background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);">
                                <div class="card-pattern" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48cGF0aCBkPSJNMjUsMjVIMTBWMTBoMTVabTAsMTVIMTBWMjVoMTVabTE1LTE1SDI1VjEwaDEwdjVoNVptMCwxNUgyNVYyNWgxMHY1aDVaIiBmaWxsPSIjZmZmZmZmIiBvcGFjaXR5PSIwLjEiLz48L3N2Zz4=');"></div>
                                <div class="d-flex justify-content-between align-items-center position-relative z-index-1">
                                    <div class="text-white">
                                        <h6 class="card-title mb-2">মোট শিক্ষার্থী</h6>
                                        <h2 class="mb-0 fw-bold"><?php echo $stats['students'] ?? 0; ?></h2>
                                        <p class="mt-2 mb-0">
                                            <a href="students.php" class="text-white text-decoration-none">
                                                <small><i class="fas fa-arrow-right me-1"></i> বিস্তারিত দেখুন</small>
                                            </a>
                                        </p>
                                    </div>
                                    <div class="icon-circle bg-white bg-opacity-25 p-3 rounded-circle">
                                        <i class="fas fa-user-graduate fa-2x text-white"></i>
                                    </div>
                                </div>
                                <div class="card-shine"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Fee Management Card -->
                    <div class="col-md-3 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body position-relative overflow-hidden" style="background: linear-gradient(135deg, #20c997 0%, #0ca678 100%);">
                                <div class="card-pattern" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48cGF0aCBkPSJNMjUsMjVIMTBWMTBoMTVabTAsMTVIMTBWMjVoMTVabTE1LTE1SDI1VjEwaDEwdjVoNVptMCwxNUgyNVYyNWgxMHY1aDVaIiBmaWxsPSIjZmZmZmZmIiBvcGFjaXR5PSIwLjEiLz48L3N2Zz4=');"></div>
                                <div class="d-flex justify-content-between align-items-center position-relative z-index-1">
                                    <div class="text-white">
                                        <h6 class="card-title mb-2">ফি ম্যানেজমেন্ট</h6>
                                        <h2 class="mb-0 fw-bold"><i class="fas fa-money-bill-wave"></i></h2>
                                        <p class="mt-2 mb-0">
                                            <a href="fee_management.php" class="text-white text-decoration-none">
                                                <small><i class="fas fa-arrow-right me-1"></i> ফি ম্যানেজমেন্ট পেজে যান</small>
                                            </a>
                                        </p>
                                    </div>
                                    <div class="icon-circle bg-white bg-opacity-25 p-3 rounded-circle">
                                        <i class="fas fa-money-bill-wave fa-2x text-white"></i>
                                    </div>
                                </div>
                                <div class="card-shine"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Due Fees Card -->
                    <div class="col-md-3 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body position-relative overflow-hidden" style="background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);">
                                <div class="card-pattern" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48cGF0aCBkPSJNMjUsMjVIMTBWMTBoMTVabTAsMTVIMTBWMjVoMTVabTE1LTE1SDI1VjEwaDEwdjVoNVptMCwxNUgyNVYyNWgxMHY1aDVaIiBmaWxsPSIjZmZmZmZmIiBvcGFjaXR5PSIwLjEiLz48L3N2Zz4=');"></div>
                                <div class="d-flex justify-content-between align-items-center position-relative z-index-1">
                                    <div class="text-white">
                                        <h6 class="card-title mb-2">বকেয়া বেতন</h6>
                                        <h2 class="mb-0 fw-bold"><i class="fas fa-exclamation-circle"></i></h2>
                                        <p class="mt-2 mb-0">
                                            <a href="due_fees.php" class="text-white text-decoration-none">
                                                <small><i class="fas fa-arrow-right me-1"></i> বকেয়া বেতন পেজে যান</small>
                                            </a>
                                        </p>
                                    </div>
                                    <div class="icon-circle bg-white bg-opacity-25 p-3 rounded-circle">
                                        <i class="fas fa-exclamation-circle fa-2x text-white"></i>
                                    </div>
                                </div>
                                <div class="card-shine"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body position-relative overflow-hidden" style="background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);">
                                <div class="card-pattern" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48cGF0aCBkPSJNMjUsMjVIMTBWMTBoMTVabTAsMTVIMTBWMjVoMTVabTE1LTE1SDI1VjEwaDEwdjVoNVptMCwxNUgyNVYyNWgxMHY1aDVaIiBmaWxsPSIjZmZmZmZmIiBvcGFjaXR5PSIwLjEiLz48L3N2Zz4=');"></div>
                                <div class="d-flex justify-content-between align-items-center position-relative z-index-1">
                                    <div class="text-white">
                                        <h6 class="card-title mb-2">মোট শিক্ষক</h6>
                                        <h2 class="mb-0 fw-bold"><?php echo $stats['teachers'] ?? 0; ?></h2>
                                        <p class="mt-2 mb-0">
                                            <a href="teachers.php" class="text-white text-decoration-none">
                                                <small><i class="fas fa-arrow-right me-1"></i> বিস্তারিত দেখুন</small>
                                            </a>
                                        </p>
                                    </div>
                                    <div class="icon-circle bg-white bg-opacity-25 p-3 rounded-circle">
                                        <i class="fas fa-chalkboard-teacher fa-2x text-white"></i>
                                    </div>
                                </div>
                                <div class="card-shine"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body position-relative overflow-hidden" style="background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);">
                                <div class="card-pattern" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48cGF0aCBkPSJNMjUsMjVIMTBWMTBoMTVabTAsMTVIMTBWMjVoMTVabTE1LTE1SDI1VjEwaDEwdjVoNVptMCwxNUgyNVYyNWgxMHY1aDVaIiBmaWxsPSIjZmZmZmZmIiBvcGFjaXR5PSIwLjEiLz48L3N2Zz4=');"></div>
                                <div class="d-flex justify-content-between align-items-center position-relative z-index-1">
                                    <div class="text-white">
                                        <h6 class="card-title mb-2">মোট কর্মচারী</h6>
                                        <h2 class="mb-0 fw-bold"><?php echo $stats['staff'] ?? 0; ?></h2>
                                        <p class="mt-2 mb-0">
                                            <a href="staff.php" class="text-white text-decoration-none">
                                                <small><i class="fas fa-arrow-right me-1"></i> বিস্তারিত দেখুন</small>
                                            </a>
                                        </p>
                                    </div>
                                    <div class="icon-circle bg-white bg-opacity-25 p-3 rounded-circle">
                                        <i class="fas fa-user-tie fa-2x text-white"></i>
                                    </div>
                                </div>
                                <div class="card-shine"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body position-relative overflow-hidden" style="background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);">
                                <div class="card-pattern" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48cGF0aCBkPSJNMjUsMjVIMTBWMTBoMTVabTAsMTVIMTBWMjVoMTVabTE1LTE1SDI1VjEwaDEwdjVoNVptMCwxNUgyNVYyNWgxMHY1aDVaIiBmaWxsPSIjZmZmZmZmIiBvcGFjaXR5PSIwLjEiLz48L3N2Zz4=');"></div>
                                <div class="d-flex justify-content-between align-items-center position-relative z-index-1">
                                    <div class="text-white">
                                        <h6 class="card-title mb-2">মোট ক্লাস</h6>
                                        <h2 class="mb-0 fw-bold"><?php echo $stats['classes'] ?? 0; ?></h2>
                                        <p class="mt-2 mb-0">
                                            <a href="classes.php" class="text-white text-decoration-none">
                                                <small><i class="fas fa-arrow-right me-1"></i> বিস্তারিত দেখুন</small>
                                            </a>
                                        </p>
                                    </div>
                                    <div class="icon-circle bg-white bg-opacity-25 p-3 rounded-circle">
                                        <i class="fas fa-chalkboard fa-2x text-white"></i>
                                    </div>
                                </div>
                                <div class="card-shine"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- bKash Payment Statistics -->
                <div class="row mb-4">
                    <div class="col-md-6 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body position-relative overflow-hidden" style="background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);">
                                <div class="card-pattern" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48Y2lyY2xlIGN4PSIyNSIgY3k9IjI1IiByPSI1IiBmaWxsPSIjZmZmZmZmIiBvcGFjaXR5PSIwLjEiLz48Y2lyY2xlIGN4PSI1IiBjeT0iNSIgcj0iNSIgZmlsbD0iI2ZmZmZmZiIgb3BhY2l0eT0iMC4xIi8+PGNpcmNsZSBjeD0iNDUiIGN5PSI0NSIgcj0iNSIgZmlsbD0iI2ZmZmZmZiIgb3BhY2l0eT0iMC4xIi8+PGNpcmNsZSBjeD0iNSIgY3k9IjQ1IiByPSI1IiBmaWxsPSIjZmZmZmZmIiBvcGFjaXR5PSIwLjEiLz48Y2lyY2xlIGN4PSI0NSIgY3k9IjUiIHI9IjUiIGZpbGw9IiNmZmZmZmYiIG9wYWNpdHk9IjAuMSIvPjwvc3ZnPg==');"></div>
                                <div class="d-flex justify-content-between align-items-center position-relative z-index-1">
                                    <div class="text-white">
                                        <h6 class="card-title mb-2">বিকাশ পেমেন্ট সংখ্যা</h6>
                                        <h2 class="mb-0 fw-bold"><?php echo $bkashStats['total_payments'] ?? 0; ?></h2>
                                        <div class="mt-3 d-flex align-items-center">
                                            <div class="bg-white bg-opacity-25 rounded-pill px-2 py-1 me-2">
                                                <i class="fas fa-chart-line text-white"></i>
                                            </div>
                                            <span class="small">সকল পেমেন্ট ট্রানজেকশন</span>
                                        </div>
                                    </div>
                                    <div class="icon-circle bg-white bg-opacity-25 p-3 rounded-circle">
                                        <i class="fas fa-mobile-alt fa-2x text-white"></i>
                                    </div>
                                </div>
                                <div class="mt-3 position-relative z-index-1">
                                    <a href="bkash_dashboard.php" class="btn btn-light btn-sm">
                                        <i class="fas fa-chart-bar me-1"></i> বিকাশ ড্যাশবোর্ড দেখুন
                                    </a>
                                </div>
                                <div class="card-shine"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card stat-card h-100">
                            <div class="card-body position-relative overflow-hidden" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
                                <div class="card-pattern" style="background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1MCIgaGVpZ2h0PSI1MCIgdmlld0JveD0iMCAwIDUwIDUwIj48Y2lyY2xlIGN4PSIyNSIgY3k9IjI1IiByPSI1IiBmaWxsPSIjZmZmZmZmIiBvcGFjaXR5PSIwLjEiLz48Y2lyY2xlIGN4PSI1IiBjeT0iNSIgcj0iNSIgZmlsbD0iI2ZmZmZmZiIgb3BhY2l0eT0iMC4xIi8+PGNpcmNsZSBjeD0iNDUiIGN5PSI0NSIgcj0iNSIgZmlsbD0iI2ZmZmZmZiIgb3BhY2l0eT0iMC4xIi8+PGNpcmNsZSBjeD0iNSIgY3k9IjQ1IiByPSI1IiBmaWxsPSIjZmZmZmZmIiBvcGFjaXR5PSIwLjEiLz48Y2lyY2xlIGN4PSI0NSIgY3k9IjUiIHI9IjUiIGZpbGw9IiNmZmZmZmYiIG9wYWNpdHk9IjAuMSIvPjwvc3ZnPg==');"></div>
                                <div class="d-flex justify-content-between align-items-center position-relative z-index-1">
                                    <div class="text-white">
                                        <h6 class="card-title mb-2">বিকাশ পেমেন্ট পরিমাণ</h6>
                                        <h2 class="mb-0 fw-bold">৳ <?php echo number_format($bkashStats['completed_amount'] ?? 0, 2); ?></h2>
                                        <div class="mt-3 d-flex align-items-center">
                                            <div class="bg-white bg-opacity-25 rounded-pill px-2 py-1 me-2">
                                                <i class="fas fa-check-circle text-white"></i>
                                            </div>
                                            <span class="small">সম্পূর্ণ হওয়া পেমেন্ট</span>
                                        </div>
                                    </div>
                                    <div class="icon-circle bg-white bg-opacity-25 p-3 rounded-circle">
                                        <i class="fas fa-hand-holding-usd fa-2x text-white"></i>
                                    </div>
                                </div>
                                <div class="mt-3 position-relative z-index-1">
                                    <a href="bkash_payment_list.php" class="btn btn-light btn-sm">
                                        <i class="fas fa-list me-1"></i> পেমেন্ট তালিকা দেখুন
                                    </a>
                                </div>
                                <div class="card-shine"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <!-- Recent Students -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-user-graduate text-primary me-2"></i>সাম্প্রতিক শিক্ষার্থী
                                </h5>
                                <a href="add_student.php" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus-circle me-1"></i> নতুন যোগ করুন
                                </a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>নাম</th>
                                                <th>আইডি</th>
                                                <th>বিভাগ</th>
                                                <th>তারিখ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($recentStudents && $recentStudents->num_rows > 0): ?>
                                                <?php while ($student = $recentStudents->fetch_assoc()): ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="avatar-circle bg-primary text-white me-2">
                                                                    <?php
                                                                    $initials = mb_substr($student['first_name'], 0, 1, 'UTF-8');
                                                                    echo $initials;
                                                                    ?>
                                                                </div>
                                                                <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>
                                                            </div>
                                                        </td>
                                                        <td><span class="badge bg-light text-dark"><?php echo htmlspecialchars($student['student_id']); ?></span></td>
                                                        <td><?php echo htmlspecialchars($student['department_name'] ?? 'N/A'); ?></td>
                                                        <td><?php echo date('d/m/Y', strtotime($student['admission_date'])); ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="fas fa-info-circle me-1"></i> কোন সাম্প্রতিক শিক্ষার্থী পাওয়া যায়নি
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-end mt-3">
                                    <a href="students.php" class="btn btn-outline-primary">
                                        <i class="fas fa-list me-1"></i> সকল শিক্ষার্থী দেখান
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Teachers -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-chalkboard-teacher text-success me-2"></i>সাম্প্রতিক শিক্ষক
                                </h5>
                                <a href="add_teacher.php" class="btn btn-sm btn-success">
                                    <i class="fas fa-plus-circle me-1"></i> নতুন যোগ করুন
                                </a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>নাম</th>
                                                <th>আইডি</th>
                                                <th>বিভাগ</th>
                                                <th>তারিখ</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($recentTeachers && $recentTeachers->num_rows > 0): ?>
                                                <?php while ($teacher = $recentTeachers->fetch_assoc()): ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="avatar-circle bg-success text-white me-2">
                                                                    <?php
                                                                    $initials = mb_substr($teacher['first_name'], 0, 1, 'UTF-8');
                                                                    echo $initials;
                                                                    ?>
                                                                </div>
                                                                <?php echo htmlspecialchars($teacher['first_name'] . ' ' . $teacher['last_name']); ?>
                                                            </div>
                                                        </td>
                                                        <td><span class="badge bg-light text-dark"><?php echo htmlspecialchars($teacher['teacher_id']); ?></span></td>
                                                        <td><?php echo htmlspecialchars($teacher['department_name'] ?? 'N/A'); ?></td>
                                                        <td><?php echo date('d/m/Y', strtotime($teacher['joining_date'])); ?></td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="fas fa-info-circle me-1"></i> কোন সাম্প্রতিক শিক্ষক পাওয়া যায়নি
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-end mt-3">
                                    <a href="teachers.php" class="btn btn-outline-success">
                                        <i class="fas fa-list me-1"></i> সকল শিক্ষক দেখান
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <style>
                    .avatar-circle {
                        width: 30px;
                        height: 30px;
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-weight: bold;
                    }
                </style>

                <div class="row">
                    <!-- Upcoming Exams -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-file-alt text-warning me-2"></i>আসন্ন পরীক্ষা
                                </h5>
                                <a href="create_exam.php" class="btn btn-sm btn-warning">
                                    <i class="fas fa-plus-circle me-1"></i> নতুন পরীক্ষা
                                </a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>পরীক্ষা</th>
                                                <th>বিষয়</th>
                                                <th>তারিখ</th>
                                                <th>মার্ক</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($upcomingExams && $upcomingExams->num_rows > 0): ?>
                                                <?php while ($exam = $upcomingExams->fetch_assoc()): ?>
                                                    <tr>
                                                        <td>
                                                            <div class="fw-medium"><?php echo htmlspecialchars($exam['exam_name']); ?></div>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($exam['subject_name'] ?? 'N/A'); ?></td>
                                                        <td>
                                                            <span class="badge bg-light text-dark">
                                                                <i class="far fa-calendar-alt me-1"></i>
                                                                <?php echo date('d/m/Y', strtotime($exam['exam_date'])); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-warning text-dark">
                                                                <?php echo htmlspecialchars($exam['total_marks']); ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="4" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="fas fa-info-circle me-1"></i> কোন আসন্ন পরীক্ষা পাওয়া যায়নি
                                                        </div>
                                                        <div class="mt-2">
                                                            <a href="create_exam.php" class="btn btn-sm btn-warning">
                                                                <i class="fas fa-plus-circle me-1"></i> নতুন পরীক্ষা যোগ করুন
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="text-end mt-3">
                                    <a href="manage_exams.php" class="btn btn-outline-warning">
                                        <i class="fas fa-list me-1"></i> সকল পরীক্ষা দেখান
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Notices -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-bullhorn text-danger me-2"></i>সাম্প্রতিক নোটিশ
                                </h5>
                                <a href="add_notice.php" class="btn btn-sm btn-danger">
                                    <i class="fas fa-plus-circle me-1"></i> নতুন নোটিশ
                                </a>
                            </div>
                            <div class="card-body">
                                <?php
                                // Check if notices table exists
                                $noticesTableCheck = $conn->query("SHOW TABLES LIKE 'notices'");

                                if ($noticesTableCheck && $noticesTableCheck->num_rows > 0) {
                                    // Get recent notices
                                    $notice_query = "SELECT * FROM notices ORDER BY date DESC LIMIT 5";
                                    $notice_result = $conn->query($notice_query);

                                    if ($notice_result && $notice_result->num_rows > 0) {
                                        echo '<div class="notice-list">';
                                        while ($notice = $notice_result->fetch_assoc()) {
                                            echo '<div class="notice-item p-3 mb-2 border rounded bg-light">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6 class="mb-0 fw-bold">' . htmlspecialchars($notice['title']) . '</h6>
                                                    <span class="badge bg-danger rounded-pill">
                                                        <i class="far fa-calendar-alt me-1"></i> ' . date('d/m/Y', strtotime($notice['date'])) . '
                                                    </span>
                                                </div>
                                                <p class="mb-2 text-truncate small">' .
                                                    (isset($notice['content']) ? htmlspecialchars(substr($notice['content'], 0, 100)) . '...' : 'কোন বিবরণ নেই') .
                                                '</p>
                                                <div class="text-end">
                                                    <a href="view_notice.php?id=' . $notice['id'] . '" class="btn btn-sm btn-outline-danger">
                                                        <i class="fas fa-eye me-1"></i> বিস্তারিত দেখুন
                                                    </a>
                                                </div>
                                            </div>';
                                        }
                                        echo '</div>';
                                    } else {
                                        echo '<div class="text-center py-4">
                                            <div class="text-muted mb-3">
                                                <i class="fas fa-info-circle me-1"></i> কোন নোটিশ পাওয়া যায়নি
                                            </div>
                                            <a href="add_notice.php" class="btn btn-sm btn-danger">
                                                <i class="fas fa-plus-circle me-1"></i> নতুন নোটিশ যোগ করুন
                                            </a>
                                        </div>';
                                    }
                                } else {
                                    echo '<div class="text-center py-4">
                                        <div class="text-danger mb-3">
                                            <i class="fas fa-exclamation-triangle me-1"></i> নোটিশ টেবিল পাওয়া যায়নি
                                        </div>
                                        <a href="../create_notices_table.php" class="btn btn-danger">
                                            <i class="fas fa-database me-1"></i> নোটিশ টেবিল তৈরি করুন
                                        </a>
                                    </div>';
                                }
                                ?>

                                <?php if ($noticesTableCheck && $noticesTableCheck->num_rows > 0 && isset($notice_result) && $notice_result->num_rows > 0): ?>
                                    <div class="text-end mt-3">
                                        <a href="notices.php" class="btn btn-outline-danger">
                                            <i class="fas fa-list me-1"></i> সকল নোটিশ দেখান
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

